# Teacher Analytics API Documentation

## Tổng quan
Bộ API Teacher Analytics cung cấp các công cụ phân tích chuyên sâu cho giảng viên để đánh giá hiệu quả quiz và hiểu rõ tình hình học tập của học sinh.

## 🆕 Cập nhật mới (Latest Updates)

### ✨ NEW CHART ENDPOINTS - Biểu đồ tương tác
- **Student Groups Chart**: Bi<PERSON>u đồ cột nhóm học sinh với click để xem chi tiết
- **Learning Outcomes Chart**: <PERSON><PERSON><PERSON><PERSON> đồ cột LO với click để xem chi tiết
- **Student Group Detail**: Chi tiết nhóm học sinh khi click vào cột
- **Learning Outcome Detail**: Chi tiết LO khi click vào cột
- **Student LO Analysis**: Phân tích điểm mạnh/yếu theo LO của từng học sinh

### 🎯 Tính năng chính
- **Interactive Charts**: Click vào cột để xem chi tiết
- **Color Coding**: <PERSON><PERSON><PERSON> sắc theo performance level
- **Chart.js Ready**: Dữ liệu sẵn sàng cho Chart.js/React Charts
- **Mobile Responsive**: Hỗ trợ responsive design
- **LO Description**: Bao gồm mô tả chi tiết của Learning Outcomes

## Base URL
```
/api/teacher-analytics
```

## Authentication
Tất cả API yêu cầu authentication token và chỉ dành cho role `admin` và `teacher`.

## API Endpoints

## 📊 NEW CHART ENDPOINTS (Biểu đồ tương tác)

### 1. Student Groups Chart Data
**GET** `/quiz/:quizId/student-groups`

Lấy dữ liệu để vẽ biểu đồ cột nhóm học sinh với khả năng click để xem chi tiết.

**Response:**
```json
{
  "success": true,
  "data": {
    "chart_data": [
      {
        "group_name": "excellent",
        "display_name": "Xuất sắc",
        "student_count": 12,
        "percentage": 26.7,
        "score_range": {
          "min": 85.5,
          "max": 98.2,
          "average": 91.8
        },
        "color": "#4CAF50"
      },
      {
        "group_name": "good",
        "display_name": "Khá",
        "student_count": 18,
        "percentage": 40.0,
        "score_range": {
          "min": 70.0,
          "max": 84.9,
          "average": 77.5
        },
        "color": "#2196F3"
      },
      {
        "group_name": "average",
        "display_name": "Trung bình",
        "student_count": 10,
        "percentage": 22.2,
        "score_range": {
          "min": 50.0,
          "max": 69.9,
          "average": 59.8
        },
        "color": "#FF9800"
      },
      {
        "group_name": "weak",
        "display_name": "Yếu",
        "student_count": 5,
        "percentage": 11.1,
        "score_range": {
          "min": 20.0,
          "max": 49.9,
          "average": 35.2
        },
        "color": "#F44336"
      }
    ],
    "total_students": 45,
    "chart_config": {
      "x_axis": "group_name",
      "y_axis": "student_count",
      "tooltip_fields": ["percentage", "score_range"],
      "clickable": true
    }
  }
}
```

**Frontend Integration:**
```javascript
// Chart.js example
const chartData = {
  labels: response.data.chart_data.map(item => item.display_name),
  datasets: [{
    label: 'Số học sinh',
    data: response.data.chart_data.map(item => item.student_count),
    backgroundColor: response.data.chart_data.map(item => item.color),
    borderColor: response.data.chart_data.map(item => item.color),
    borderWidth: 1
  }]
};

// Click handler
const handleChartClick = (event, elements) => {
  if (elements.length > 0) {
    const index = elements[0].index;
    const groupName = response.data.chart_data[index].group_name;
    // Navigate to detail: /quiz/122/student-groups/excellent
    fetchStudentGroupDetail(quizId, groupName);
  }
};
```

### 2. Student Group Detail (Khi click vào cột)
**GET** `/quiz/:quizId/student-groups/:groupName`

Lấy chi tiết nhóm học sinh khi click vào cột biểu đồ.

**Parameters:**
- `groupName`: `excellent` | `good` | `average` | `weak`

**Response:**
```json
{
  "success": true,
  "data": {
    "group_info": {
      "group_name": "excellent",
      "display_name": "Xuất sắc",
      "student_count": 12,
      "average_score": 91.8,
      "average_percentage": 91.8,
      "threshold": 85
    },
    "students": [
      {
        "user_id": 120,
        "name": "Nguyễn Văn A",
        "email": "<EMAIL>",
        "score": 95.5,
        "percentage_score": 95.5,
        "completion_time": 1800,
        "average_time_per_question": 90,
        "total_questions_attempted": 20,
        "correct_answers": 19
      }
    ],
    "insights": [
      "Nhóm xuất sắc với 12 học sinh",
      "Điểm trung bình: 91.8/10 (91.8%)"
    ],
    "recommendations": [
      {
        "type": "enrichment",
        "suggestion": "Tạo thêm bài tập nâng cao cho nhóm này",
        "priority": "medium"
      }
    ]
  }
}
```

### 3. Learning Outcomes Chart Data
**GET** `/quiz/:quizId/learning-outcomes`

Lấy dữ liệu để vẽ biểu đồ cột Learning Outcomes với khả năng click để xem chi tiết.

**Response:**
```json
{
  "success": true,
  "data": {
    "chart_data": [
      {
        "lo_id": 1,
        "lo_name": "LO1",
        "short_name": "LO1",
        "accuracy": 85.5,
        "performance_level": "excellent",
        "total_questions": 5,
        "correct_answers": 42,
        "total_attempts": 50,
        "color": "#4CAF50"
      },
      {
        "lo_id": 2,
        "lo_name": "LO2",
        "short_name": "LO2",
        "accuracy": 65.2,
        "performance_level": "average",
        "total_questions": 3,
        "correct_answers": 28,
        "total_attempts": 45,
        "color": "#FF9800"
      }
    ],
    "chart_config": {
      "x_axis": "lo_name",
      "y_axis": "accuracy",
      "tooltip_fields": ["total_questions", "correct_answers", "total_attempts"],
      "clickable": true,
      "performance_thresholds": {
        "excellent": 85,
        "good": 70,
        "average": 50,
        "weak": 0
      }
    },
    "summary": {
      "total_los": 5,
      "average_accuracy": 75.8,
      "strongest_lo": {
        "lo_id": 1,
        "lo_name": "LO1",
        "accuracy": 85.5
      },
      "weakest_lo": {
        "lo_id": 3,
        "lo_name": "LO3",
        "accuracy": 45.2
      }
    }
  }
}
```

**📊 Giải thích các field trong Learning Outcomes Chart:**

- **`total_questions`**: Số câu hỏi thuộc LO này trong quiz (ví dụ: 5 câu)
- **`total_attempts`**: Tổng số lượt trả lời **ĐẦU TIÊN** của TẤT CẢ học sinh (ví dụ: 10 học sinh × 5 câu = 50 lượt)
- **`correct_answers`**: Tổng số lượt trả lời ĐÚNG **ĐẦU TIÊN** của tất cả học sinh (ví dụ: 42/50 lượt đúng)
- **`accuracy`**: Tỷ lệ đúng = (correct_answers / total_attempts) × 100 (ví dụ: 42/50 = 84%)

**🎯 Logic đánh giá:**
- **Chỉ tính lần đầu tiên** học sinh làm mỗi câu hỏi (bỏ qua retry)
- **Mục đích**: Đánh giá năng lực thực sự của học sinh, không bị ảnh hưởng bởi việc thử lại
- **Kết quả**: `correct_answers` ≤ `total_attempts` ≤ (số học sinh × `total_questions`)

**💡 Ví dụ với 10 học sinh, LO có 5 câu:**
- `total_questions = 5` (câu hỏi trong LO)
- `total_attempts = 50` (10 học sinh × 5 câu, chỉ tính lần đầu)
- `correct_answers = 42` (42/50 lượt đúng lần đầu)
- `accuracy = 84%`

### 4. Learning Outcome Detail (Khi click vào cột)
**GET** `/quiz/:quizId/learning-outcomes/:loId`

Lấy chi tiết Learning Outcome khi click vào cột biểu đồ.

**Response:**
```json
{
  "success": true,
  "data": {
    "lo_info": {
      "lo_id": 1,
      "lo_name": "LO1",
      "description": "Hiểu được các khái niệm cơ bản",
      "total_questions": 5,
      "accuracy": 85.5,
      "performance_level": "excellent"
    },
    "question_breakdown": [
      {
        "question_id": 1,
        "question_text": "Câu hỏi về khái niệm A...",
        "difficulty": "Dễ",
        "correct_count": 8,
        "total_attempts": 10,
        "accuracy": 80.0,
        "insights": ["Câu hỏi trung bình - cần ôn tập thêm"]
      }
    ],
    "student_performance": [
      {
        "performance_level": "excellent",
        "student_count": 8,
        "students": [
          {
            "user_id": 120,
            "name": "Nguyễn Văn A",
            "correct_count": 5,
            "total_count": 5,
            "accuracy": 100.0
          }
        ]
      }
    ],
    "insights": [
      "LO tốt - duy trì phương pháp hiện tại"
    ],
    "recommendations": [
      {
        "type": "content_review",
        "suggestion": "Xem xét lại nội dung câu hỏi khó",
        "priority": "medium"
      }
    ]
  }
}
```

### 5. Student LO Analysis (Chi tiết học sinh)
**GET** `/quiz/:quizId/student/:userId/lo-analysis`

Lấy phân tích chi tiết Learning Outcomes của từng học sinh - điểm mạnh/yếu theo LO.

**Response:**
```json
{
  "success": true,
  "data": {
    "student_info": {
      "user_id": 120,
      "name": "Nguyễn Văn A",
      "email": "<EMAIL>",
      "quiz_score": 8.5,
      "completion_time": 1800,
      "overall_percentage": 85.0
    },
    "quiz_info": {
      "quiz_id": 122,
      "name": "Bài tập Thiết kế web",
      "total_questions": 20
    },
    "lo_analysis": [
      {
        "lo_id": 1,
        "lo_name": "LO1",
        "lo_description": "Hiểu được các thẻ HTML cơ bản và cách sử dụng",
        "total_questions": 5,
        "attempted_questions": 5,
        "correct_answers": 5,
        "achievement_percentage": 100.0,
        "performance_level": "excellent",
        "status": "Xuất sắc",
        "color": "#4CAF50",
        "average_time_seconds": 120.5,
        "total_time_seconds": 602,
        "insights": [
          "Nắm vững hoàn toàn LO này (100.0%)"
        ],
        "recommendations": [
          "Có thể làm mentor cho bạn khác về LO này"
        ],
        "question_details": [
          {
            "question_id": 1,
            "question_text": "Thẻ HTML nào dùng để tạo tiêu đề?",
            "difficulty": "Dễ",
            "is_correct": true,
            "time_spent": 95,
            "attempted": true
          }
        ]
      },
      {
        "lo_id": 2,
        "lo_name": "LO2",
        "lo_description": "Áp dụng CSS để tạo layout và styling",
        "total_questions": 3,
        "attempted_questions": 3,
        "correct_answers": 1,
        "achievement_percentage": 33.3,
        "performance_level": "weak",
        "status": "Yếu",
        "color": "#F44336",
        "average_time_seconds": 180.2,
        "insights": [
          "Chưa nắm được LO này (33.3%)",
          "Thời gian suy nghĩ lâu - có thể chưa tự tin"
        ],
        "recommendations": [
          "Cần học lại từ đầu và được hỗ trợ thêm",
          "Luyện tập thêm để tăng tốc độ giải quyết"
        ]
      }
    ],
    "summary": {
      "total_los": 5,
      "strengths_count": 3,
      "weaknesses_count": 1,
      "needs_improvement_count": 1,
      "strongest_lo": {
        "lo_name": "LO1",
        "percentage": 100.0
      },
      "weakest_lo": {
        "lo_name": "LO2",
        "percentage": 33.3
      }
    },
    "insights": [
      "Điểm mạnh: 3 LO đạt từ 70% trở lên",
      "Điểm yếu: 1 LO dưới 50%",
      "Cần cải thiện: 1 LO từ 50-70%"
    ],
    "recommendations": [
      {
        "type": "focus",
        "suggestion": "Tập trung cải thiện LO2",
        "priority": "high"
      }
    ]
  }
}
```

## 🎨 Frontend Integration Guide

### Chart.js Integration Example

```javascript
// 1. Student Groups Chart
const createStudentGroupsChart = (data) => {
  const ctx = document.getElementById('studentGroupsChart').getContext('2d');

  return new Chart(ctx, {
    type: 'bar',
    data: {
      labels: data.chart_data.map(item => item.display_name),
      datasets: [{
        label: 'Số học sinh',
        data: data.chart_data.map(item => item.student_count),
        backgroundColor: data.chart_data.map(item => item.color),
        borderColor: data.chart_data.map(item => item.color),
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      onClick: (event, elements) => {
        if (elements.length > 0) {
          const index = elements[0].index;
          const groupName = data.chart_data[index].group_name;
          showStudentGroupDetail(quizId, groupName);
        }
      },
      plugins: {
        tooltip: {
          callbacks: {
            afterLabel: (context) => {
              const item = data.chart_data[context.dataIndex];
              return [
                `Tỷ lệ: ${item.percentage}%`,
                `Điểm TB: ${item.score_range.average}`,
                `Khoảng: ${item.score_range.min} - ${item.score_range.max}`
              ];
            }
          }
        }
      }
    }
  });
};

// 2. Learning Outcomes Chart
const createLearningOutcomesChart = (data) => {
  const ctx = document.getElementById('learningOutcomesChart').getContext('2d');

  return new Chart(ctx, {
    type: 'bar',
    data: {
      labels: data.chart_data.map(item => item.lo_name),
      datasets: [{
        label: 'Độ chính xác (%)',
        data: data.chart_data.map(item => item.accuracy),
        backgroundColor: data.chart_data.map(item => item.color),
        borderColor: data.chart_data.map(item => item.color),
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true,
          max: 100,
          ticks: {
            callback: (value) => value + '%'
          }
        }
      },
      onClick: (event, elements) => {
        if (elements.length > 0) {
          const index = elements[0].index;
          const loId = data.chart_data[index].lo_id;
          showLearningOutcomeDetail(quizId, loId);
        }
      }
    }
  });
};

// 3. Student LO Analysis (Radar Chart)
const createStudentLORadarChart = (data) => {
  const ctx = document.getElementById('studentLOChart').getContext('2d');

  return new Chart(ctx, {
    type: 'radar',
    data: {
      labels: data.lo_analysis.map(lo => lo.lo_name),
      datasets: [{
        label: 'Phần trăm đạt được',
        data: data.lo_analysis.map(lo => lo.achievement_percentage),
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 2,
        pointBackgroundColor: data.lo_analysis.map(lo => lo.color),
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: data.lo_analysis.map(lo => lo.color)
      }]
    },
    options: {
      responsive: true,
      scales: {
        r: {
          beginAtZero: true,
          max: 100,
          ticks: {
            callback: (value) => value + '%'
          }
        }
      }
    }
  });
};
```

### React Component Example

```jsx
import React, { useState, useEffect } from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { Bar } from 'react-chartjs-2';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const StudentGroupsChart = ({ quizId }) => {
  const [chartData, setChartData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStudentGroupsData();
  }, [quizId]);

  const fetchStudentGroupsData = async () => {
    try {
      const response = await fetch(`/api/teacher-analytics/quiz/${quizId}/student-groups`);
      const data = await response.json();
      setChartData(data.data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching student groups data:', error);
      setLoading(false);
    }
  };

  const handleChartClick = (event, elements) => {
    if (elements.length > 0) {
      const index = elements[0].index;
      const groupName = chartData.chart_data[index].group_name;
      // Navigate to detail page or show modal
      window.location.href = `/teacher/quiz/${quizId}/groups/${groupName}`;
    }
  };

  if (loading) return <div>Loading...</div>;
  if (!chartData) return <div>No data available</div>;

  const data = {
    labels: chartData.chart_data.map(item => item.display_name),
    datasets: [{
      label: 'Số học sinh',
      data: chartData.chart_data.map(item => item.student_count),
      backgroundColor: chartData.chart_data.map(item => item.color),
      borderColor: chartData.chart_data.map(item => item.color),
      borderWidth: 1
    }]
  };

  const options = {
    responsive: true,
    onClick: handleChartClick,
    plugins: {
      legend: {
        position: 'top'
      },
      title: {
        display: true,
        text: 'Phân bố nhóm học sinh'
      },
      tooltip: {
        callbacks: {
          afterLabel: (context) => {
            const item = chartData.chart_data[context.dataIndex];
            return [
              `Tỷ lệ: ${item.percentage}%`,
              `Điểm TB: ${item.score_range.average}`,
              `Khoảng: ${item.score_range.min} - ${item.score_range.max}`
            ];
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1
        }
      }
    }
  };

  return (
    <div className="chart-container">
      <Bar data={data} options={options} />
      <p className="chart-note">
        💡 Click vào cột để xem chi tiết nhóm học sinh
      </p>
    </div>
  );
};

export default StudentGroupsChart;
```

### Difficulty-LO Distribution Heatmap Component

```jsx
import React, { useState, useEffect } from 'react';
import * as d3 from 'd3';

const DifficultyLOHeatmap = ({ quizId }) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchHeatmapData();
  }, [quizId]);

  const fetchHeatmapData = async () => {
    try {
      const response = await fetch(`/api/teacher-analytics/quiz/${quizId}/difficulty-lo-distribution`);
      const result = await response.json();
      setData(result.data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching heatmap data:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (data) {
      createHeatmap();
    }
  }, [data]);

  const createHeatmap = () => {
    // Clear previous chart
    d3.select("#heatmap-container").selectAll("*").remove();

    const margin = {top: 50, right: 100, bottom: 100, left: 100};
    const width = 600 - margin.left - margin.right;
    const height = 400 - margin.top - margin.bottom;

    const svg = d3.select("#heatmap-container")
      .append("svg")
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom)
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Create scales
    const xScale = d3.scaleBand()
      .domain(data.axes_data.learning_outcomes.map(lo => lo.lo_name))
      .range([0, width])
      .padding(0.1);

    const yScale = d3.scaleBand()
      .domain(data.axes_data.difficulty_levels.map(level => level.level_name))
      .range([height, 0])
      .padding(0.1);

    // Color scale based on accuracy
    const colorScale = d3.scaleSequential(d3.interpolateRdYlGn)
      .domain([0, 100]);

    // Create tooltip
    const tooltip = d3.select("body").append("div")
      .attr("class", "tooltip")
      .style("opacity", 0)
      .style("position", "absolute")
      .style("background", "rgba(0,0,0,0.8)")
      .style("color", "white")
      .style("padding", "10px")
      .style("border-radius", "5px")
      .style("pointer-events", "none");

    // Create heatmap cells
    svg.selectAll("rect")
      .data(data.matrix_data)
      .enter()
      .append("rect")
      .attr("x", d => xScale(d.lo_name))
      .attr("y", d => yScale(d.level_name))
      .attr("width", xScale.bandwidth())
      .attr("height", yScale.bandwidth())
      .attr("fill", d => colorScale(d.accuracy))
      .attr("stroke", "#fff")
      .attr("stroke-width", 2)
      .style("cursor", "pointer")
      .on("mouseover", (event, d) => {
        tooltip.transition().duration(200).style("opacity", .9);
        tooltip.html(`
          <strong>${d.lo_name} - ${d.level_name}</strong><br/>
          Số câu hỏi: ${d.question_count}<br/>
          Độ chính xác: ${d.accuracy}%<br/>
          Thời gian TB: ${d.average_time_seconds}s<br/>
          Đánh giá: ${d.difficulty_assessment}
        `)
        .style("left", (event.pageX + 10) + "px")
        .style("top", (event.pageY - 28) + "px");
      })
      .on("mouseout", () => {
        tooltip.transition().duration(500).style("opacity", 0);
      });

    // Add question count labels
    svg.selectAll("text.count")
      .data(data.matrix_data)
      .enter()
      .append("text")
      .attr("class", "count")
      .attr("x", d => xScale(d.lo_name) + xScale.bandwidth()/2)
      .attr("y", d => yScale(d.level_name) + yScale.bandwidth()/2)
      .attr("text-anchor", "middle")
      .attr("dominant-baseline", "middle")
      .attr("fill", d => d.accuracy > 50 ? "#000" : "#fff")
      .attr("font-weight", "bold")
      .text(d => d.question_count);

    // Add axes
    svg.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale))
      .selectAll("text")
      .style("text-anchor", "end")
      .attr("dx", "-.8em")
      .attr("dy", ".15em")
      .attr("transform", "rotate(-45)");

    svg.append("g")
      .call(d3.axisLeft(yScale));

    // Add axis labels
    svg.append("text")
      .attr("transform", "rotate(-90)")
      .attr("y", 0 - margin.left)
      .attr("x", 0 - (height / 2))
      .attr("dy", "1em")
      .style("text-anchor", "middle")
      .text("Độ khó");

    svg.append("text")
      .attr("transform", `translate(${width / 2}, ${height + margin.bottom - 10})`)
      .style("text-anchor", "middle")
      .text("Learning Outcomes");

    // Add color legend
    const legendWidth = 200;
    const legendHeight = 20;

    const legend = svg.append("g")
      .attr("transform", `translate(${width + 20}, 50)`);

    const legendScale = d3.scaleLinear()
      .domain([0, 100])
      .range([0, legendWidth]);

    const legendAxis = d3.axisBottom(legendScale)
      .ticks(5)
      .tickFormat(d => d + "%");

    // Create gradient
    const gradient = svg.append("defs")
      .append("linearGradient")
      .attr("id", "legend-gradient")
      .attr("x1", "0%").attr("y1", "0%")
      .attr("x2", "100%").attr("y2", "0%");

    gradient.selectAll("stop")
      .data(d3.range(0, 101, 10))
      .enter().append("stop")
      .attr("offset", d => d + "%")
      .attr("stop-color", d => colorScale(d));

    legend.append("rect")
      .attr("width", legendWidth)
      .attr("height", legendHeight)
      .style("fill", "url(#legend-gradient)");

    legend.append("g")
      .attr("transform", `translate(0, ${legendHeight})`)
      .call(legendAxis);

    legend.append("text")
      .attr("x", legendWidth / 2)
      .attr("y", -5)
      .style("text-anchor", "middle")
      .text("Độ chính xác (%)");
  };

  if (loading) return <div>Loading heatmap...</div>;
  if (!data) return <div>No data available</div>;

  return (
    <div className="heatmap-container">
      <h3>Phân bố câu hỏi theo độ khó và Learning Outcomes</h3>
      <div id="heatmap-container"></div>

      <div className="stats-summary">
        <h4>Thống kê phân bố:</h4>
        <div className="balance-info">
          <p><strong>Đánh giá cân bằng:</strong> {data.distribution_stats.balance_analysis.balance_assessment}</p>
          <div className="difficulty-breakdown">
            <span className="easy">Dễ: {data.distribution_stats.balance_analysis.easy_percentage}%</span>
            <span className="medium">TB: {data.distribution_stats.balance_analysis.medium_percentage}%</span>
            <span className="hard">Khó: {data.distribution_stats.balance_analysis.hard_percentage}%</span>
          </div>
        </div>

        {data.insights.length > 0 && (
          <div className="insights">
            <h5>Nhận xét:</h5>
            <ul>
              {data.insights.map((insight, index) => (
                <li key={index}>{insight}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default DifficultyLOHeatmap;
```

## 📱 Mobile Responsive Considerations

```css
.chart-container {
  position: relative;
  height: 400px;
  width: 100%;
}

.heatmap-container {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #fff;
}

.heatmap-container h3 {
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

.stats-summary {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 5px;
}

.balance-info {
  margin: 10px 0;
}

.difficulty-breakdown {
  display: flex;
  gap: 15px;
  margin-top: 10px;
}

.difficulty-breakdown span {
  padding: 5px 10px;
  border-radius: 3px;
  font-weight: bold;
}

.difficulty-breakdown .easy {
  background: #4CAF50;
  color: white;
}

.difficulty-breakdown .medium {
  background: #FF9800;
  color: white;
}

.difficulty-breakdown .hard {
  background: #F44336;
  color: white;
}

.insights ul {
  margin: 10px 0;
  padding-left: 20px;
}

.insights li {
  margin: 5px 0;
  color: #666;
}

.tooltip {
  font-size: 12px;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .chart-container {
    height: 300px;
  }

  .heatmap-container {
    padding: 10px;
  }

  .heatmap-container svg {
    max-width: 100%;
    height: auto;
  }

  .difficulty-breakdown {
    flex-direction: column;
    gap: 5px;
  }

  /* Adjust chart labels for mobile */
  .chart-container canvas {
    max-height: 300px;
  }
}
```

## 🔄 API Flow Diagram

```
1. Teacher Dashboard
   ↓
2. GET /quiz/:quizId/student-groups (Chart data)
   ↓
3. Render interactive bar chart
   ↓
4. User clicks on "Excellent" column
   ↓
5. GET /quiz/:quizId/student-groups/excellent (Detail)
   ↓
6. Show student list with scores
   ↓
7. User clicks on specific student
   ↓
8. GET /quiz/:quizId/student/:userId/lo-analysis
   ↓
9. Show student's LO strengths/weaknesses
```

---

## 📊 EXISTING ENDPOINTS (Đã có từ trước)

### 8. Comprehensive Quiz Report
**GET** `/quiz/:quizId/comprehensive-report`

Lấy báo cáo tổng quan chi tiết về quiz với phân tích điểm mạnh/yếu và insights.

**Response:**
```json
{
  "success": true,
  "data": {
    "quiz_info": {
      "quiz_id": 1,
      "name": "Quiz Toán học",
      "total_questions": 20
    },
    "overall_performance": {
      "total_participants": 45,
      "average_score": 72.5,
      "completion_rate": 88.9
    },
    "learning_outcome_analysis": [
      {
        "lo_name": "Giải phương trình",
        "accuracy": 65.2,
        "performance_level": "average",
        "insights": ["LO trung bình - cần cải thiện"],
        "recommendations": ["Tăng cường luyện tập"]
      }
    ],
    "student_groups": {
      "excellent": { "count": 12, "percentage": 26.7 },
      "good": { "count": 18, "percentage": 40.0 },
      "average": { "count": 10, "percentage": 22.2 },
      "weak": { "count": 5, "percentage": 11.1 }
    },
    "teacher_insights": [
      {
        "category": "strengths",
        "message": "Học sinh nắm vững LO cơ bản",
        "priority": "maintain"
      }
    ]
  }
}
```

### 6. Difficulty-LO Distribution Matrix (Biểu đồ phân bố) 🆕
**GET** `/quiz/:quizId/difficulty-lo-distribution`

Lấy dữ liệu phân bố câu hỏi theo độ khó và Learning Outcomes để vẽ biểu đồ matrix/heatmap 2D.

**Response:**
```json
{
  "success": true,
  "data": {
    "quiz_info": {
      "quiz_id": 122,
      "name": "Bài tập Thiết kế web",
      "total_questions": 12
    },
    "matrix_data": [
      {
        "lo_id": 1,
        "lo_name": "LO1",
        "level_id": 1,
        "level_name": "Dễ",
        "question_count": 2,
        "total_attempts": 4,
        "correct_attempts": 4,
        "accuracy": 100.0,
        "average_time_seconds": 120.5,
        "color": "#4CAF50",
        "intensity": 0.8,
        "difficulty_assessment": "Dễ",
        "cell_size": 30,
        "students_attempted": 15,
        "success_rate": 1.0,
        "difficulty_score": 0.0,
        "cell_label": "2",
        "tooltip_text": "LO1 - Dễ: 2 câu, 100.0% đúng",
        "questions": [
          {
            "question_id": 1,
            "question_text": "Thẻ HTML nào dùng để tạo tiêu đề?"
          }
        ]
      },
      {
        "lo_id": 1,
        "lo_name": "LO1",
        "level_id": 2,
        "level_name": "Trung bình",
        "question_count": 1,
        "total_attempts": 2,
        "correct_attempts": 1,
        "accuracy": 50.0,
        "average_time_seconds": 180.2,
        "color": "#F44336",
        "intensity": 0.4,
        "difficulty_assessment": "Khó",
        "questions": [
          {
            "question_id": 5,
            "question_text": "Cách tối ưu hóa CSS cho responsive design?"
          }
        ]
      }
    ],
    "chart_config": {
      "x_axis": "lo_name",
      "y_axis": "level_name",
      "value_field": "question_count",
      "color_field": "accuracy",
      "chart_type": "heatmap",
      "tooltip_fields": ["question_count", "accuracy", "average_time_seconds", "difficulty_assessment"]
    },
    "axes_data": {
      "learning_outcomes": [
        {
          "lo_id": 1,
          "lo_name": "LO1",
          "lo_description": "Hiểu được các thẻ HTML cơ bản"
        }
      ],
      "difficulty_levels": [
        {
          "level_id": 1,
          "level_name": "Dễ"
        },
        {
          "level_id": 2,
          "level_name": "Trung bình"
        },
        {
          "level_id": 3,
          "level_name": "Khó"
        }
      ]
    },
    "distribution_stats": {
      "total_questions": 12,
      "total_students": 2,
      "lo_distribution": {
        "LO1": 5,
        "LO2": 4,
        "LO3": 3
      },
      "level_distribution": {
        "Dễ": 6,
        "Trung bình": 4,
        "Khó": 2
      },
      "balance_analysis": {
        "easy_questions": 6,
        "medium_questions": 4,
        "hard_questions": 2,
        "easy_percentage": 50.0,
        "medium_percentage": 33.3,
        "hard_percentage": 16.7,
        "balance_assessment": "Quiz có độ khó cân bằng tốt"
      }
    },
    "insights": [
      "Phân bố độ khó hợp lý, phù hợp để đánh giá học sinh",
      "LO1 có quá nhiều câu hỏi (5) so với LO3 (3)"
    ],
    "recommendations": [
      {
        "type": "balance",
        "suggestion": "Quiz có độ khó cân bằng tốt",
        "priority": "medium"
      }
    ]
  }
}
```

**📊 Giải thích các trường trong Matrix Data:**

- **`question_count`**: Số câu hỏi trong ô matrix này
- **`accuracy`**: Độ chính xác (%) - dùng để tô màu
- **`color`**: Màu sắc hex code sẵn sàng sử dụng
- **`cell_size`**: Kích thước cell được tính sẵn (10-60px)
- **`students_attempted`**: Số học sinh đã thử ô này
- **`success_rate`**: Tỷ lệ thành công (0-1) - dễ sử dụng cho scale
- **`difficulty_score`**: Điểm độ khó (0-1) - càng cao càng khó
- **`cell_label`**: Text hiển thị trong cell (số câu hỏi)
- **`tooltip_text`**: Text sẵn sàng cho tooltip

**🎨 Color Mapping:**
- 🟢 `#4CAF50` (Green): Accuracy ≥ 80% (Dễ)
- 🟠 `#FF9800` (Orange): Accuracy 60-79% (Trung bình)
- 🔴 `#F44336` (Red): Accuracy 40-59% (Khó)
- 🟣 `#9C27B0` (Purple): Accuracy < 40% (Rất khó)

**Frontend Integration (Heatmap Chart):**
```javascript
// Chart.js Heatmap example (Optimized with new fields)
const createDifficultyLOHeatmap = (data) => {
  const ctx = document.getElementById('difficultyLOChart').getContext('2d');

  // Prepare data for heatmap using new fields
  const heatmapData = data.matrix_data.map((item, index) => ({
    x: item.lo_name,
    y: item.level_name,
    v: item.question_count,
    r: item.cell_size, // Use pre-calculated cell size
    backgroundColor: item.color, // Use pre-calculated color
    borderColor: '#fff',
    // Store original item for tooltip
    originalData: item
  }));

  return new Chart(ctx, {
    type: 'bubble', // Better for heatmap with varying sizes
    data: {
      datasets: [{
        label: 'Phân bố câu hỏi',
        data: heatmapData.map(item => ({
          x: data.axes_data.learning_outcomes.findIndex(lo => lo.lo_name === item.x),
          y: data.axes_data.difficulty_levels.findIndex(level => level.level_name === item.y),
          r: item.r / 2, // Radius for bubble
          backgroundColor: item.backgroundColor,
          borderColor: item.borderColor,
          borderWidth: 2,
          originalData: item.originalData
        }))
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: {
          type: 'linear',
          position: 'bottom',
          min: -0.5,
          max: data.axes_data.learning_outcomes.length - 0.5,
          ticks: {
            stepSize: 1,
            callback: (value) => {
              const lo = data.axes_data.learning_outcomes[value];
              return lo ? lo.lo_name : '';
            }
          },
          title: {
            display: true,
            text: 'Learning Outcomes'
          }
        },
        y: {
          type: 'linear',
          min: -0.5,
          max: data.axes_data.difficulty_levels.length - 0.5,
          ticks: {
            stepSize: 1,
            callback: (value) => {
              const level = data.axes_data.difficulty_levels[value];
              return level ? level.level_name : '';
            }
          },
          title: {
            display: true,
            text: 'Độ khó'
          }
        }
      },
      plugins: {
        tooltip: {
          callbacks: {
            title: (context) => {
              const item = context[0].raw.originalData;
              return `${item.lo_name} - ${item.level_name}`;
            },
            label: (context) => {
              const item = context.raw.originalData;
              return [
                `📊 ${item.cell_label} câu hỏi`,
                `✅ ${item.accuracy}% chính xác`,
                `⏱️ ${item.average_time_seconds}s trung bình`,
                `👥 ${item.students_attempted} học sinh`,
                `📈 ${item.difficulty_assessment}`
              ];
            }
          }
        },
        legend: {
          display: false
        }
      },
      onClick: (event, elements) => {
        if (elements.length > 0) {
          const item = elements[0].element.$context.raw.originalData;
          console.log('Clicked cell:', item);
          // Handle click - show question details
          showCellDetails(item);
        }
      }
    }
  });
};

// Helper function to show cell details
const showCellDetails = (cellData) => {
  const modal = document.createElement('div');
  modal.innerHTML = `
    <div class="cell-detail-modal">
      <h3>${cellData.tooltip_text}</h3>
      <div class="cell-stats">
        <p><strong>Độ chính xác:</strong> ${cellData.accuracy}%</p>
        <p><strong>Thời gian trung bình:</strong> ${cellData.average_time_seconds}s</p>
        <p><strong>Học sinh tham gia:</strong> ${cellData.students_attempted}</p>
      </div>
      <div class="questions-list">
        <h4>Câu hỏi trong ô này:</h4>
        <ul>
          ${cellData.questions.map(q => `<li>${q.question_text}</li>`).join('')}
        </ul>
      </div>
      <button onclick="this.parentElement.parentElement.remove()">Đóng</button>
    </div>
  `;
  document.body.appendChild(modal);
};

// D3.js Heatmap example (recommended for better heatmap)
const createD3Heatmap = (data) => {
  const margin = {top: 50, right: 50, bottom: 100, left: 100};
  const width = 600 - margin.left - margin.right;
  const height = 400 - margin.top - margin.bottom;

  const svg = d3.select("#heatmap")
    .append("svg")
    .attr("width", width + margin.left + margin.right)
    .attr("height", height + margin.top + margin.bottom)
    .append("g")
    .attr("transform", `translate(${margin.left},${margin.top})`);

  // Create scales
  const xScale = d3.scaleBand()
    .domain(data.axes_data.learning_outcomes.map(lo => lo.lo_name))
    .range([0, width])
    .padding(0.1);

  const yScale = d3.scaleBand()
    .domain(data.axes_data.difficulty_levels.map(level => level.level_name))
    .range([height, 0])
    .padding(0.1);

  // Create color scale based on accuracy
  const colorScale = d3.scaleSequential(d3.interpolateRdYlGn)
    .domain([0, 100]);

  // Create heatmap cells
  svg.selectAll("rect")
    .data(data.matrix_data)
    .enter()
    .append("rect")
    .attr("x", d => xScale(d.lo_name))
    .attr("y", d => yScale(d.level_name))
    .attr("width", xScale.bandwidth())
    .attr("height", yScale.bandwidth())
    .attr("fill", d => colorScale(d.accuracy))
    .attr("stroke", "#fff")
    .attr("stroke-width", 2);

  // Add text labels
  svg.selectAll("text")
    .data(data.matrix_data)
    .enter()
    .append("text")
    .attr("x", d => xScale(d.lo_name) + xScale.bandwidth()/2)
    .attr("y", d => yScale(d.level_name) + yScale.bandwidth()/2)
    .attr("text-anchor", "middle")
    .attr("dominant-baseline", "middle")
    .attr("fill", d => d.accuracy > 50 ? "#000" : "#fff")
    .text(d => d.question_count);

  // Add axes
  svg.append("g")
    .attr("transform", `translate(0,${height})`)
    .call(d3.axisBottom(xScale));

  svg.append("g")
    .call(d3.axisLeft(yScale));
};
```

### 7. Student LO Analysis (Chi tiết học sinh theo LO) 🆕
**GET** `/quiz/:quizId/student/:userId/lo-analysis`

Lấy phân tích chi tiết Learning Outcomes của từng học sinh - điểm mạnh/yếu theo LO.

**Parameters:**
- `userId`: ID của học sinh cần phân tích

**Response:** *(Xem phần NEW CHART ENDPOINTS ở trên)*

---

## 📊 EXISTING ENDPOINTS (Đã có từ trước)

### 9. Quiz Comparison
**GET** `/quiz-comparison`

So sánh performance giữa các quiz.

**Query Parameters:**
- `quiz_ids`: Danh sách quiz_id cách nhau bởi dấu phẩy (ví dụ: 1,2,3)
- `subject_id`: So sánh tất cả quiz trong subject

**Response:**
```json
{
  "success": true,
  "data": {
    "quiz_comparisons": [
      {
        "quiz_id": 1,
        "quiz_name": "Quiz 1",
        "average_score": 72.5,
        "completion_rate": 88.9,
        "overall_accuracy": 65.2
      }
    ],
    "comparison_insights": [
      {
        "type": "performance_comparison",
        "message": "Quiz 1 có điểm cao nhất (72.5)"
      }
    ],
    "summary": {
      "best_performing_quiz": {...},
      "worst_performing_quiz": {...}
    }
  }
}
```

### 10. Student Detailed Analysis
**GET** `/quiz/:quizId/student/:userId/detailed-analysis`

Phân tích chi tiết cá nhân học sinh với insights từng câu hỏi.

**Response:**
```json
{
  "success": true,
  "data": {
    "student_info": {
      "user_id": 123,
      "name": "Nguyễn Văn A",
      "email": "<EMAIL>"
    },
    "performance_summary": {
      "score": 65,
      "accuracy": 65.0,
      "total_questions": 20,
      "correct_answers": 13
    },
    "overall_insights": {
      "insights": ["Kết quả trung bình - có thể cải thiện"],
      "recommendations": ["Tăng cường luyện tập"]
    },
    "question_by_question_analysis": [
      {
        "question_id": 1,
        "question_text": "2 + 2 = ?",
        "is_correct": true,
        "time_spent": 45,
        "insights": ["Trả lời nhanh và chính xác"],
        "recommendations": []
      }
    ]
  }
}
```

### 11. Teaching Insights
**GET** `/quiz/:quizId/teaching-insights`

Lấy insights và recommendations tổng hợp cho giảng viên.

**Response:**
```json
{
  "success": true,
  "data": {
    "summary_insights": {
      "overall_assessment": "mixed",
      "key_strengths": ["Điểm trung bình tốt"],
      "main_challenges": ["2 LO cần cải thiện"],
      "immediate_actions_needed": 2
    },
    "detailed_insights": {
      "curriculum_insights": [
        {
          "type": "weakness",
          "message": "2 LO có hiệu suất thấp",
          "impact": "high"
        }
      ],
      "teaching_method_insights": [...],
      "student_insights": [...],
      "action_recommendations": [
        {
          "category": "curriculum_revision",
          "action": "Xem xét điều chỉnh nội dung giảng dạy",
          "priority": "high",
          "timeline": "immediate"
        }
      ]
    }
  }
}
```

### 12. Quiz Benchmark
**GET** `/quiz/:quizId/benchmark`

Lấy benchmark và so sánh với historical data.

**Query Parameters:**
- `compare_with_subject`: So sánh với quiz khác trong subject (default: true)
- `compare_with_teacher`: So sánh với quiz của cùng giảng viên (default: true)

**Response:**
```json
{
  "success": true,
  "data": {
    "current_quiz": {
      "quiz_id": 1,
      "name": "Quiz Toán",
      "metrics": {
        "average_score": 72.5,
        "completion_rate": 88.9,
        "pass_rate": 75.6,
        "excellence_rate": 26.7
      }
    },
    "comparisons": {
      "subject_benchmark": {
        "comparison_base": "8 quiz khác trong subject",
        "subject_average": {
          "average_score": 68.2,
          "completion_rate": 82.1
        },
        "current_vs_average": {
          "score_difference": 4.3,
          "completion_difference": 6.8
        }
      }
    },
    "performance_ranking": {
      "current_rank": 2,
      "total_quizzes": 9,
      "percentile": 88.9,
      "ranking_insights": "Top performer"
    },
    "insights": [
      {
        "type": "positive",
        "category": "performance",
        "message": "Quiz này có điểm cao hơn 4.3 điểm so với trung bình"
      }
    ],
    "recommendations": [
      {
        "category": "improvement",
        "suggestion": "Duy trì phương pháp hiện tại",
        "priority": "medium"
      }
    ]
  }
}
```

## Error Responses

Tất cả API trả về error theo format:
```json
{
  "success": false,
  "error": "Mô tả lỗi",
  "details": "Chi tiết lỗi (nếu có)"
}
```

## Status Codes
- `200`: Success
- `400`: Bad Request (tham số không hợp lệ)
- `401`: Unauthorized (chưa đăng nhập)
- `403`: Forbidden (không có quyền truy cập)
- `404`: Not Found (không tìm thấy resource)
- `500`: Internal Server Error

## Use Cases

### 1. Đánh giá tổng quan quiz
```javascript
// Lấy báo cáo tổng quan
const response = await fetch('/api/teacher-analytics/quiz/123/comprehensive-report');
const data = await response.json();
```

### 2. Vẽ biểu đồ cột nhóm học sinh
```javascript
// Lấy dữ liệu cho biểu đồ cột nhóm học sinh
const response = await fetch('/api/teacher-analytics/quiz/123/student-groups');
const chartData = await response.json();

// Sử dụng với Chart.js hoặc thư viện biểu đồ khác
const chart = new Chart(ctx, {
  type: 'bar',
  data: {
    labels: chartData.data.chart_data.map(item => item.display_name),
    datasets: [{
      data: chartData.data.chart_data.map(item => item.student_count),
      backgroundColor: chartData.data.chart_data.map(item => item.color)
    }]
  },
  options: {
    onClick: (event, elements) => {
      if (elements.length > 0) {
        const index = elements[0].index;
        const groupName = chartData.data.chart_data[index].group_name;
        // Gọi API chi tiết nhóm
        loadGroupDetail(groupName);
      }
    }
  }
});
```

### 3. Chi tiết nhóm học sinh khi click cột
```javascript
// Xem chi tiết nhóm học sinh khi click vào cột biểu đồ
async function loadGroupDetail(groupType) {
  const response = await fetch(`/api/teacher-analytics/quiz/123/student-groups/${groupType}`);
  const groupDetail = await response.json();

  // Hiển thị danh sách sinh viên và kết quả theo LO
  displayStudentList(groupDetail.data.students);
}
```

### 4. Vẽ biểu đồ cột Learning Outcomes
```javascript
// Lấy dữ liệu cho biểu đồ cột LO
const response = await fetch('/api/teacher-analytics/quiz/123/learning-outcomes');
const loData = await response.json();

// Vẽ biểu đồ cột LO
const loChart = new Chart(loCtx, {
  type: 'bar',
  data: {
    labels: loData.data.chart_data.map(item => item.short_name),
    datasets: [{
      label: 'Độ chính xác (%)',
      data: loData.data.chart_data.map(item => item.accuracy),
      backgroundColor: loData.data.chart_data.map(item => item.color)
    }]
  },
  options: {
    onClick: (event, elements) => {
      if (elements.length > 0) {
        const index = elements[0].index;
        const loId = loData.data.chart_data[index].lo_id;
        // Gọi API chi tiết LO
        loadLODetail(loId);
      }
    }
  }
});
```

### 5. Chi tiết Learning Outcome khi click cột
```javascript
// Xem chi tiết LO khi click vào cột biểu đồ
async function loadLODetail(loId) {
  const response = await fetch(`/api/teacher-analytics/quiz/123/learning-outcomes/${loId}`);
  const loDetail = await response.json();

  // Hiển thị phân tích chi tiết LO
  displayLOAnalysis(loDetail.data);
}
```

### 6. So sánh với quiz khác
```javascript
// So sánh với các quiz trong subject
const response = await fetch('/api/teacher-analytics/quiz-comparison?subject_id=5');
const comparison = await response.json();
```

### 7. Lấy insights giảng dạy
```javascript
// Lấy recommendations cho giảng viên
const response = await fetch('/api/teacher-analytics/quiz/123/teaching-insights');
const insights = await response.json();
```
