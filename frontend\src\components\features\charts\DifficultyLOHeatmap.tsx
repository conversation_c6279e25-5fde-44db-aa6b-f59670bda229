"use client";

import React, { useState, useEffect, useRef } from "react";
import * as d3 from "d3";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/layout";
import { <PERSON><PERSON> } from "@/components/ui/forms";
import { Badge } from "@/components/ui/feedback";
import { Progress } from "@/components/ui/feedback";
import {
  Loader2,
  BarChart3,
  TrendingUp,
  AlertCircle,
  Grid3X3,
  Target,
  BookOpen,
  Award,
  RefreshCw,
  CheckCircle,
  Users,
} from "lucide-react";
import { chapterAnalyticsService } from "@/lib/services/api/chapter-analytics.service";
import { showErrorToast } from "@/lib/utils/toast-utils";

interface DifficultyLOHeatmapProps {
  quizId: number;
  className?: string;
}

interface MatrixDataItem {
  lo_id: number;
  lo_name: string;
  level_id: number;
  level_name: string;
  question_count: number;
  total_attempts: number;
  correct_attempts: number;
  accuracy: number;
  average_time_seconds: number;
  color: string;
  intensity: number;
  difficulty_assessment: string;
  cell_size: number;
  students_attempted: number;
  success_rate: number;
  difficulty_score: number;
  cell_label: string;
  tooltip_text: string;
  questions: Array<{
    question_id: number;
    question_text: string;
  }>;
}

interface LearningOutcome {
  lo_id: number;
  lo_name: string;
  lo_description: string;
}

interface DifficultyLevel {
  level_id: number;
  level_name: string;
}

interface DistributionStats {
  total_questions: number;
  total_students: number;
  lo_distribution: Record<string, number>;
  level_distribution: Record<string, number>;
  balance_analysis: {
    easy_questions: number;
    medium_questions: number;
    hard_questions: number;
    easy_percentage: number;
    medium_percentage: number;
    hard_percentage: number;
    balance_assessment: string;
  };
}

interface DifficultyLODistributionData {
  quiz_info: {
    quiz_id: number;
    name: string;
    total_questions: number;
  };
  matrix_data: MatrixDataItem[];
  chart_config: {
    x_axis: string;
    y_axis: string;
    value_field: string;
    color_field: string;
    chart_type: string;
    tooltip_fields: string[];
  };
  axes_data: {
    learning_outcomes: LearningOutcome[];
    difficulty_levels: DifficultyLevel[];
  };
  distribution_stats: DistributionStats;
  insights: string[];
  recommendations: Array<{
    type: string;
    suggestion: string;
    priority: string;
  }>;
}

const DifficultyLOHeatmap: React.FC<DifficultyLOHeatmapProps> = ({
  quizId,
  className = "",
}) => {
  const [data, setData] = useState<DifficultyLODistributionData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCell, setSelectedCell] = useState<MatrixDataItem | null>(null);
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement | null>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  useEffect(() => {
    fetchHeatmapData();
  }, [quizId]);

  useEffect(() => {
    if (data && svgRef.current) {
      createHeatmap();
    }
  }, [data]);

  // Redraw on container resize for responsiveness
  useEffect(() => {
    if (!containerRef.current) return;

    // Use ResizeObserver for responsive redraw
    if (typeof ResizeObserver !== "undefined") {
      resizeObserverRef.current = new ResizeObserver(() => {
        if (data) createHeatmap();
      });
      resizeObserverRef.current.observe(containerRef.current);
    }

    return () => {
      if (resizeObserverRef.current && containerRef.current) {
        resizeObserverRef.current.unobserve(containerRef.current);
      }
    };
  }, [data]);

  const fetchHeatmapData = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("Fetching difficulty-LO distribution data for quiz:", quizId);

      const response =
        await chapterAnalyticsService.getDifficultyLODistribution(quizId);
      console.log("Difficulty-LO distribution data received:", response);

      setData(response);
    } catch (error: any) {
      console.error("Error fetching difficulty-LO distribution data:", error);
      setError(error.message || "Không thể tải dữ liệu phân bố độ khó - LO");
      showErrorToast(
        error.message || "Không thể tải dữ liệu phân bố độ khó - LO"
      );
    } finally {
      setLoading(false);
    }
  };

  const createHeatmap = () => {
    if (!data || !svgRef.current) return;

    // Clear previous chart
    d3.select(svgRef.current).selectAll("*").remove();

    // Layout and responsive sizing
    const margin = { top: 20, right: 20, bottom: 80, left: 120 };
    const containerWidth = containerRef.current?.clientWidth || 800;
    const xCategories = data.axes_data.learning_outcomes.map(
      (lo) => lo.lo_name
    );
    const yCategories = data.axes_data.difficulty_levels.map(
      (level) => level.level_name
    );

    // Adaptive cell sizes
    const minCellWidth = 48;
    const minCellHeight = 36;
    const width =
      Math.max(xCategories.length * minCellWidth, containerWidth - 40) -
      margin.left -
      margin.right;
    const height =
      Math.max(yCategories.length * minCellHeight, 240) -
      margin.top -
      margin.bottom;

    const svg = d3
      .select(svgRef.current)
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom);

    const g = svg
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Scales
    const xScale = d3
      .scaleBand()
      .domain(xCategories)
      .range([0, width])
      .padding(0.1);
    const yScale = d3
      .scaleBand()
      .domain(yCategories)
      .range([height, 0])
      .padding(0.1);

    // Color scale based on accuracy
    const colorScale = d3
      .scaleSequential(d3.interpolateRdYlGn)
      .domain([0, 100]);

    // Helper: pick contrasting text color based on fill
    const getTextColor = (accuracy: number) => {
      const col = d3.color(colorScale(accuracy)) as d3.RGBColor | null;
      if (!col) return "#000";
      const r = col.r / 255,
        gCol = col.g / 255,
        b = col.b / 255;
      const [R, G, B] = [r, gCol, b].map((v) =>
        v <= 0.03928 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4)
      );
      const luminance = 0.2126 * R + 0.7152 * G + 0.0722 * B;
      return luminance > 0.5 ? "#111827" : "#FFFFFF"; // slate-900 or white
    };

    // Gridlines for bands
    g.append("g")
      .selectAll("line.h-grid")
      .data(xCategories)
      .enter()
      .append("line")
      .attr("class", "h-grid")
      .attr("x1", (d) => xScale(d) || 0)
      .attr("y1", 0)
      .attr("x2", (d) => xScale(d) || 0)
      .attr("y2", height)
      .attr("stroke", "#e5e7eb")
      .attr("stroke-width", 1)
      .attr("opacity", 0.6);

    g.append("g")
      .selectAll("line.v-grid")
      .data(yCategories)
      .enter()
      .append("line")
      .attr("class", "v-grid")
      .attr("x1", 0)
      .attr("y1", (d) => yScale(d) || 0)
      .attr("x2", width)
      .attr("y2", (d) => yScale(d) || 0)
      .attr("stroke", "#e5e7eb")
      .attr("stroke-width", 1)
      .attr("opacity", 0.6);

    // Single tooltip node (managed by D3 but owned by React via ref)
    if (!tooltipRef.current) {
      const el = document.createElement("div");
      el.className =
        "pointer-events-none fixed z-50 bg-gray-900 text-white p-3 rounded-lg shadow-lg text-sm max-w-xs opacity-0 transition-opacity";
      document.body.appendChild(el);
      tooltipRef.current = el;
    }

    const showTooltip = (event: any, d: MatrixDataItem) => {
      if (!tooltipRef.current) return;
      tooltipRef.current.innerHTML = `
        <div class="font-bold mb-2">${d.lo_name} - ${d.level_name}</div>
        <div class="space-y-1">
          <div>Số câu hỏi: <span class="font-semibold">${
            d.question_count
          }</span></div>
          <div>Độ chính xác: <span class="font-semibold">${d.accuracy.toFixed(
            1
          )}%</span></div>
          <div>Thời gian TB: <span class="font-semibold">${d.average_time_seconds.toFixed(
            1
          )}s</span></div>
          <div>Học sinh thử: <span class="font-semibold">${
            d.students_attempted
          }</span></div>
          <div>Đánh giá: <span class="font-semibold">${
            d.difficulty_assessment
          }</span></div>
        </div>
      `;
      const padding = 10;
      const x = event.pageX + padding;
      const y = event.pageY - 28;
      tooltipRef.current.style.left = `${x}px`;
      tooltipRef.current.style.top = `${y}px`;
      tooltipRef.current.style.opacity = "1";
    };

    const hideTooltip = () => {
      if (tooltipRef.current) tooltipRef.current.style.opacity = "0";
    };

    // Cells
    g.selectAll("rect.heatmap-cell")
      .data(data.matrix_data)
      .enter()
      .append("rect")
      .attr("class", "heatmap-cell")
      .attr("x", (d: MatrixDataItem) => xScale(d.lo_name) || 0)
      .attr("y", (d: MatrixDataItem) => yScale(d.level_name) || 0)
      .attr("rx", 6)
      .attr("ry", 6)
      .attr("width", xScale.bandwidth())
      .attr("height", yScale.bandwidth())
      .attr("fill", (d: MatrixDataItem) => colorScale(d.accuracy))
      .attr("stroke", (d: MatrixDataItem) =>
        selectedCell?.lo_id === d.lo_id && selectedCell?.level_id === d.level_id
          ? "#3b82f6"
          : "#fff"
      )
      .attr("stroke-width", (d: MatrixDataItem) =>
        selectedCell?.lo_id === d.lo_id && selectedCell?.level_id === d.level_id
          ? 3
          : 2
      )
      .style("cursor", "pointer")
      .on("mouseover", function (event: any, d: MatrixDataItem) {
        d3.select(this)
          .attr("stroke-width", 3)
          .style("filter", "brightness(1.05)");
        showTooltip(event, d);
      })
      .on("mousemove", function (event: any, d: MatrixDataItem) {
        showTooltip(event, d);
      })
      .on("mouseout", function () {
        d3.select(this).attr("stroke-width", 2).style("filter", "none");
        hideTooltip();
      })
      .on("click", (_event: any, d: MatrixDataItem) => {
        setSelectedCell((prev) =>
          prev?.lo_id === d.lo_id && prev?.level_id === d.level_id ? null : d
        );
      })
      .append("title")
      .text((d) => `${d.lo_name} - ${d.level_name}`);

    // Question count labels with contrast-aware color
    g.selectAll("text.count-label")
      .data(data.matrix_data)
      .enter()
      .append("text")
      .attr("class", "count-label")
      .attr(
        "x",
        (d: MatrixDataItem) => (xScale(d.lo_name) || 0) + xScale.bandwidth() / 2
      )
      .attr(
        "y",
        (d: MatrixDataItem) =>
          (yScale(d.level_name) || 0) + yScale.bandwidth() / 2
      )
      .attr("text-anchor", "middle")
      .attr("dominant-baseline", "middle")
      .attr("fill", (d: MatrixDataItem) => getTextColor(d.accuracy))
      .attr("font-weight", "700")
      .attr("font-size", "13px")
      .text((d: MatrixDataItem) => d.question_count)
      .style("pointer-events", "none");

    // Axes with improved label management
    const xAxis = d3.axisBottom(xScale).tickFormat((d: any) => {
      const label = String(d);
      return label.length > 12 ? label.slice(0, 12) + "…" : label;
    });

    g.append("g")
      .attr("class", "x-axis")
      .attr("transform", `translate(0,${height})`)
      .call(xAxis)
      .selectAll("text")
      .style("text-anchor", "end")
      .attr("dx", "-.8em")
      .attr("dy", ".15em")
      .attr("transform", "rotate(-35)")
      .style("font-size", "12px")
      .append("title")
      .text((d: any) => String(d));

    g.append("g")
      .attr("class", "y-axis")
      .call(d3.axisLeft(yScale))
      .selectAll("text")
      .style("font-size", "12px");

    // Legend gradient (0-100%)
    const legendHeight = 10;
    const legendWidth = Math.min(240, Math.max(160, width * 0.4));
    const legendX = (width - legendWidth) / 2;
    const legendY = height + 30;

    const defs = svg.append("defs");
    const gradientId = "accuracy-gradient";
    const linearGradient = defs
      .append("linearGradient")
      .attr("id", gradientId)
      .attr("x1", "0%")
      .attr("y1", "0%")
      .attr("x2", "100%")
      .attr("y2", "0%");

    [0, 25, 50, 75, 100].forEach((p) => {
      linearGradient
        .append("stop")
        .attr("offset", `${p}%`)
        .attr("stop-color", colorScale(p));
    });

    const legendGroup = svg
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    legendGroup
      .append("rect")
      .attr("x", legendX)
      .attr("y", legendY)
      .attr("width", legendWidth)
      .attr("height", legendHeight)
      .attr("rx", 5)
      .attr("ry", 5)
      .attr("fill", `url(#${gradientId})`);

    const legendScale = d3
      .scaleLinear()
      .domain([0, 100])
      .range([legendX, legendX + legendWidth]);
    const legendAxis = d3
      .axisBottom(legendScale)
      .ticks(5)
      .tickFormat((d) => `${d}%`);

    legendGroup
      .append("g")
      .attr("transform", `translate(0, ${legendY + legendHeight})`)
      .call(legendAxis)
      .selectAll("text")
      .style("font-size", "10px");
  };

  const handleCellClick = (cell: MatrixDataItem) => {
    if (
      selectedCell?.lo_id === cell.lo_id &&
      selectedCell?.level_id === cell.level_id
    ) {
      setSelectedCell(null); // Collapse if clicking the same cell
    } else {
      setSelectedCell(cell); // Expand new cell
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-96">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
            <span>Đang tải dữ liệu phân bố...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-96">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchHeatmapData} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Thử lại
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-96">
          <p>Không có dữ liệu phân bố</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Heatmap Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Grid3X3 className="h-6 w-6 text-primary" />
            Phân bố câu hỏi theo độ khó và Learning Outcomes
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Hover để xem chi tiết, click vào ô để xem thông tin bên dưới
          </p>
        </CardHeader>
        <CardContent>
          <div ref={containerRef} className="w-full">
            <svg
              ref={svgRef}
              className="w-full"
              role="img"
              aria-label="Biểu đồ nhiệt phân bố độ khó theo Learning Outcomes"
            ></svg>
          </div>

          {/* Color Legend */}
          <div className="mt-4 flex items-center justify-center space-x-4 text-sm">
            <span className="text-muted-foreground">Độ chính xác:</span>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-500 rounded"></div>
              <span>Thấp</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-yellow-500 rounded"></div>
              <span>Trung bình</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-500 rounded"></div>
              <span>Cao</span>
            </div>
          </div>

          {/* Matrix Legend with Selection Indicator */}
          <div className="mt-6 pt-4 border-t">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {data.matrix_data.map((cell) => (
                <div
                  key={`${cell.lo_name}-${cell.level_name}`}
                  className={`flex items-center justify-between p-3 rounded-lg border transition-all cursor-pointer ${
                    selectedCell?.lo_id === cell.lo_id &&
                    selectedCell?.level_id === cell.level_id
                      ? "bg-primary/10 ring-2 ring-primary/20 border-primary/30"
                      : "hover:bg-muted/50 border-border"
                  }`}
                  onClick={() => handleCellClick(cell)}
                >
                  <div className="flex items-center gap-2">
                    <div
                      className="w-4 h-4 rounded border-2 border-white"
                      style={{
                        backgroundColor: d3
                          .scaleSequential(d3.interpolateRdYlGn)
                          .domain([0, 100])(cell.accuracy),
                      }}
                    />
                    <div>
                      <div
                        className={`text-xs font-medium ${
                          selectedCell?.lo_id === cell.lo_id &&
                          selectedCell?.level_id === cell.level_id
                            ? "text-primary"
                            : ""
                        }`}
                      >
                        {cell.lo_name} - {cell.level_name}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {cell.question_count} câu • {cell.accuracy.toFixed(1)}%
                      </div>
                    </div>
                  </div>
                  {selectedCell?.lo_id === cell.lo_id &&
                    selectedCell?.level_id === cell.level_id && (
                      <CheckCircle className="h-4 w-4 text-primary" />
                    )}
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Selected Cell Details */}
      {selectedCell && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-6 w-6 text-primary" />
              Chi tiết: {selectedCell.lo_name} - {selectedCell.level_name}
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Thông tin chi tiết về ô được chọn
            </p>
          </CardHeader>
          <CardContent>
            {/* Metrics Grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {selectedCell.question_count}
                </div>
                <div className="text-sm text-blue-700">Số câu hỏi</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {selectedCell.accuracy.toFixed(1)}%
                </div>
                <div className="text-sm text-green-700">Độ chính xác</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">
                  {selectedCell.average_time_seconds.toFixed(1)}s
                </div>
                <div className="text-sm text-orange-700">Thời gian TB</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {selectedCell.students_attempted}
                </div>
                <div className="text-sm text-purple-700">Học sinh thử</div>
              </div>
            </div>

            {/* Assessment Badge */}
            <div className="mb-4">
              <Badge
                variant={
                  selectedCell.accuracy >= 80
                    ? "default"
                    : selectedCell.accuracy >= 60
                    ? "secondary"
                    : selectedCell.accuracy >= 40
                    ? "outline"
                    : "destructive"
                }
                className="text-sm"
              >
                {selectedCell.difficulty_assessment}
              </Badge>
            </div>

            {/* Questions List */}
            {selectedCell.questions && selectedCell.questions.length > 0 && (
              <div>
                <h5 className="font-semibold text-sm mb-3 flex items-center gap-2">
                  <BookOpen className="h-4 w-4" />
                  Danh sách câu hỏi ({selectedCell.questions.length})
                </h5>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {selectedCell.questions.map((question, index) => (
                    <div
                      key={question.question_id}
                      className="p-3 bg-muted/30 rounded-lg border-l-4 border-primary/30"
                    >
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center text-xs font-medium mt-0.5">
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <p className="text-sm text-foreground leading-relaxed">
                            {question.question_text}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Distribution Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Balance Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-6 w-6 text-primary" />
              Cân bằng độ khó
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground">
              <strong>Đánh giá:</strong>{" "}
              {data.distribution_stats.balance_analysis.balance_assessment}
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm">Dễ</span>
                </div>
                <div className="flex items-center gap-2">
                  <Progress
                    value={
                      data.distribution_stats.balance_analysis.easy_percentage
                    }
                    className="w-20 h-2"
                  />
                  <Badge variant="secondary" className="text-xs">
                    {data.distribution_stats.balance_analysis.easy_percentage.toFixed(
                      1
                    )}
                    %
                  </Badge>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm">Trung bình</span>
                </div>
                <div className="flex items-center gap-2">
                  <Progress
                    value={
                      data.distribution_stats.balance_analysis.medium_percentage
                    }
                    className="w-20 h-2"
                  />
                  <Badge variant="secondary" className="text-xs">
                    {data.distribution_stats.balance_analysis.medium_percentage.toFixed(
                      1
                    )}
                    %
                  </Badge>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-sm">Khó</span>
                </div>
                <div className="flex items-center gap-2">
                  <Progress
                    value={
                      data.distribution_stats.balance_analysis.hard_percentage
                    }
                    className="w-20 h-2"
                  />
                  <Badge variant="secondary" className="text-xs">
                    {data.distribution_stats.balance_analysis.hard_percentage.toFixed(
                      1
                    )}
                    %
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* LO Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-6 w-6 text-primary" />
              Phân bố Learning Outcomes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(data.distribution_stats.lo_distribution).map(
                ([lo, count]) => (
                  <div key={lo} className="flex items-center justify-between">
                    <span className="text-sm font-medium">{lo}</span>
                    <Badge variant="outline" className="text-xs">
                      {count} câu
                    </Badge>
                  </div>
                )
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Insights and Recommendations */}
      {Array.isArray(data.insights) && data.insights.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-6 w-6 text-primary" />
              Nhận xét và Khuyến nghị
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Insights */}
              <div className="space-y-3">
                <h4 className="font-semibold text-sm flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Nhận xét
                </h4>
                <ul className="space-y-2">
                  {data.insights.map((insight: string, index: number) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-primary mt-1 text-xs">•</span>
                      <span className="text-sm text-muted-foreground">
                        {insight}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Recommendations */}
              {Array.isArray(data.recommendations) &&
                data.recommendations.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="font-semibold text-sm flex items-center gap-2">
                      <Target className="h-4 w-4" />
                      Khuyến nghị
                    </h4>
                    <div className="space-y-3">
                      {data.recommendations.map((rec: any, index: number) => (
                        <div
                          key={index}
                          className="p-3 bg-muted/30 rounded-lg border"
                        >
                          <div className="flex items-center gap-2 mb-2">
                            <Badge
                              variant={
                                rec.priority === "high"
                                  ? "destructive"
                                  : rec.priority === "medium"
                                  ? "default"
                                  : "secondary"
                              }
                              className="text-xs"
                            >
                              {rec.priority === "high"
                                ? "Cao"
                                : rec.priority === "medium"
                                ? "TB"
                                : "Thấp"}
                            </Badge>
                            <span className="text-xs text-muted-foreground uppercase">
                              {rec.type}
                            </span>
                          </div>
                          <p className="text-sm text-foreground">
                            {rec.suggestion}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default DifficultyLOHeatmap;
